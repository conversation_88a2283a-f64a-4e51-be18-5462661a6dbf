import * as cdk from "aws-cdk-lib";
import {
  UserPool,
  UserPoolClient,
  OAuthScope,
  UserPoolClientIdentityProvider, // Uncomment when adding OAuth providers
} from "aws-cdk-lib/aws-cognito";
import { Construct } from "constructs";

export interface FrontendStackProps extends cdk.StackProps {
  applicationName: string;
}

export class FrontendStack extends cdk.Stack {
  private userPool: UserPool;
  private userPoolClient: UserPoolClient;

  constructor(scope: Construct, id: string, props?: FrontendStackProps) {
    super(scope, id, props);

    // Create User Pool configured for OAuth-only authentication
    this.userPool = new UserPool(this, "EAPUserPool", {
      userPoolName: `${props?.applicationName}-userpool`,
      // Configure sign-in aliases (email is commonly used with OAuth providers)
      signInAliases: {
        email: true,
        username: false, // Disable username to encourage OAuth usage
      },
      // Auto-verify email addresses
      autoVerify: {
        email: true,
      },
      // Disable password-based authentication by not allowing password sign-in
      signInCaseSensitive: false,
    });

    // Create User Pool Client configured for OAuth flows only
    this.userPoolClient = new UserPoolClient(this, "EAPUserPoolClient", {
      userPool: this.userPool,
      userPoolClientName: `${props?.applicationName}-userpool-client`,

      // Disable traditional authentication flows
      authFlows: {
        userPassword: false, // Disable username/password authentication
        userSrp: false, // Disable SRP authentication
        adminUserPassword: false, // Disable admin-initiated auth
        custom: false, // Disable custom authentication
      },

      // Configure OAuth settings
      oAuth: {
        flows: {
          authorizationCodeGrant: true, // Enable OAuth authorization code flow
          implicitCodeGrant: false, // Disable implicit flow for security
          clientCredentials: false, // Not needed for user authentication
        },
        scopes: [
          OAuthScope.OPENID, // Required for OpenID Connect
          OAuthScope.EMAIL, // Access to email
          OAuthScope.PROFILE, // Access to profile information
        ],
        // Placeholder callback URLs - these should be updated with actual application URLs
        callbackUrls: [
          "http://localhost:3000/auth/callback", // For local development
          // Add production URLs here when available
        ],
        logoutUrls: [
          "http://localhost:3000/auth/logout", // For local development
          // Add production URLs here when available
        ],
      },

      // Specify that only OAuth providers should be supported
      // Note: COGNITO provider is excluded to prevent direct user pool sign-in


      // Generate client secret for server-side applications
      generateSecret: true,

      // Prevent user existence errors for security
      preventUserExistenceErrors: true,
    });
  }

  // Getter methods to access the created resources
  public getUserPool(): UserPool {
    return this.userPool;
  }

  public getUserPoolClient(): UserPoolClient {
    return this.userPoolClient;
  }
}
