import * as cdk from "aws-cdk-lib";
import { UserPool, UserPoolClient } from "aws-cdk-lib/aws-cognito";
import { Construct } from "constructs";
// import * as sqs from 'aws-cdk-lib/aws-sqs';

export interface FrontendStackProps extends cdk.StackProps {
  applicationName: string;
}

export class FrontendStack extends cdk.Stack {
  private userPool: UserPool;
  private userPoolClient: UserPoolClient;
  constructor(scope: Construct, id: string, props?: FrontendStackProps) {
    super(scope, id, props);

    this.userPool = new UserPool(this, "EAPUserPool", {
      userPoolName: `${props?.applicationName}-userpool`,
    });

    this.userPoolClient = new UserPoolClient(this, "EAPUserPoolClient", {
      userPool: this.userPool,
      userPoolClientName: `${props?.applicationName}-userpool-client`,
    });
  }
}
