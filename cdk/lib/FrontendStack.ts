import * as cdk from "aws-cdk-lib";
import {
  UserPool,
  UserPoolClient,
  OAuthScope,
  UserPoolClientIdentityProvider, // Uncomment when adding OAuth providers
} from "aws-cdk-lib/aws-cognito";
import { Construct } from "constructs";

export interface FrontendStackProps extends cdk.StackProps {
  applicationName: string;
}

export class FrontendStack extends cdk.Stack {
  private userPool: UserPool;
  private userPoolClient: UserPoolClient;

  constructor(scope: Construct, id: string, props?: FrontendStackProps) {
    super(scope, id, props);

    // Create User Pool configured for both OAuth and username/password authentication
    this.userPool = new UserPool(this, "EAPUserPool", {
      userPoolName: `${props?.applicationName}-userpool`,
      // Configure sign-in aliases to support both username and email
      signInAliases: {
        email: true,
        username: true, // Enable username for traditional authentication
      },
      // Auto-verify email addresses
      autoVerify: {
        email: true,
      },
      // Enable case-insensitive sign-in for better user experience
      signInCaseSensitive: false,
    });

    // Create User Pool Client configured for OAuth flows only
    this.userPoolClient = new UserPoolClient(this, "EAPUserPoolClient", {
      userPool: this.userPool,
      userPoolClientName: `${props?.applicationName}-userpool-client`,

      // Disable traditional authentication flows
      authFlows: {
        userPassword: false, // Disable username/password authentication
        userSrp: false, // Disable SRP authentication
        adminUserPassword: false, // Disable admin-initiated auth
        custom: false, // Disable custom authentication
      },

      // Configure OAuth settings
      oAuth: {
        flows: {
          authorizationCodeGrant: true,
          implicitCodeGrant: false,
          clientCredentials: false,
        },
        scopes: [OAuthScope.OPENID, OAuthScope.EMAIL, OAuthScope.PROFILE],
        callbackUrls: ["http://localhost:3000/auth/callback"],
        logoutUrls: ["http://localhost:3000/auth/logout"],
      },
      supportedIdentityProviders: [UserPoolClientIdentityProvider.COGNITO],
      generateSecret: true,
      preventUserExistenceErrors: true,
    });
  }

  // Getter methods to access the created resources
  public getUserPool(): UserPool {
    return this.userPool;
  }

  public getUserPoolClient(): UserPoolClient {
    return this.userPoolClient;
  }
}
