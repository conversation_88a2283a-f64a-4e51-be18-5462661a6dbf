name: Deploy EAP Infrastructure and Frontend

on:
  push:
    branches:
      - main

jobs:
  deploy-infrastructure:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials using OIDC
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::851725421927:role/GithubActionsRole
          role-session-name: GitHubActions-CDK-Deploy
          aws-region: ap-southeast-1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.14.4

      - name: Install CDK dependencies
        working-directory: ./cdk
        run: pnpm install

      - name: Build CDK
        working-directory: ./cdk
        run: pnpm run build

      - name: Deploy CDK Stack
        working-directory: ./cdk
        run: |
          npx cdk deploy --require-approval never -c stage=dev

  build-and-push:
    needs: deploy-infrastructure
    runs-on: self-hosted
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }} # Uses the GitHub user who triggered the workflow
          password: ${{ secrets.GITHUB_TOKEN }} # Automatic token for GHCR

      - name: set lower case owner name
        run: |
          echo "OWNER_LC=${OWNER,,}" >>${GITHUB_ENV}
        env:
          OWNER: "${{ github.repository_owner }}"

      - name: Build and push Frontend Docker image to GHCR
        run: |
          # Define the full image name for GHCR
          FRONTEND_IMAGE="ghcr.io/${{ env.OWNER_LC }}/eap-frontend:main"

          # Build the image
          docker build -t $FRONTEND_IMAGE .

          # Push the image
          docker push $FRONTEND_IMAGE

      - name: Trigger Infra deployment on server
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            cd /home/<USER>/eap/infra
            git pull origin main

            echo "${{ secrets.GHCR_PAT }}" | docker login ghcr.io -u ${{ secrets.YOUR_GITHUB_USERNAME }} --password-stdin

            docker compose pull frontend

            docker compose up -d --no-build --remove-orphans frontend
